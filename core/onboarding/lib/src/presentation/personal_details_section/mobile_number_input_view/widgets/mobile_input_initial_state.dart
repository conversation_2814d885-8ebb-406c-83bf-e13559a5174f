import 'package:duplo/duplo.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:onboarding/src/analytics/onboarding_analytics.dart';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:onboarding/src/di/di_container.dart';
import 'package:onboarding/src/presentation/personal_details_section/mobile_number_input_view/bloc/mobile_number_input_bloc.dart';
import 'package:onboarding/src/presentation/personal_details_section/utils/personal_details_error.dart';

class MobileInputInitialState extends StatelessWidget {
  const MobileInputInitialState({
    super.key,
    required this.phoneNumberController,
  });
  final TextEditingController phoneNumberController;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final localization = EquitiLocalization.of(context);
    final textStyles = context.duploTextStyles;
    return BlocBuilder<MobileNumberInputBloc, MobileNumberInputState>(
      buildWhen: (previous, current) {
        return previous != current;
      },
      builder: (blocContext, state) {
        final bloc = blocContext.read<MobileNumberInputBloc>();
        final isLtr = Directionality.of(blocContext) == TextDirection.ltr;
        final isUpdatingPhoneNumber =
            state.updatePhoneArgs?.isUpdatingPhoneNumber ?? false;
        return Scaffold(
          backgroundColor: theme.background.bgPrimary,
          bottomNavigationBar: SafeArea(
            top: false,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: DuploButton.defaultPrimary(
                semanticsIdentifier: 'confirm',
                title:
                    isUpdatingPhoneNumber
                        ? localization.onboarding_continueText
                        : localization.onboarding_confirmButton,
                isLoading: state.isLoading,
                loadingText: localization.onboarding_loading,
                onTap: () {
                  final phone =
                      '${state.selectedCountryCode}${state.phoneNumber}';
                  diContainer<OnboardingAnalytics>().phoneSubmit(phone: phone);
                  return bloc.add(
                    MobileNumberInputEvent.confirmButtonPressed(),
                  );
                },
                isDisabled: !state.isConfirmButtonEnabled,
                trailingIcon:
                    Localizations.localeOf(context).toString() == 'ar'
                        ? Assets.images.chevronRight.keyName
                        : Assets.images
                            .chevronRightDirectional(blocContext)
                            .keyName,
              ),
            ),
          ),
          appBar: DuploAppBar(
            title:
                isUpdatingPhoneNumber
                    ? localization.hub_changePhoneNumber
                    : localization.onboarding_personalDetailsTitle,
          ),
          body: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (!isUpdatingPhoneNumber)
                  DuploProgressBar(progressValue: .4, currentBarIndex: 1),
                DuploText(
                  text:
                      isUpdatingPhoneNumber
                          ? localization.hub_changeYourPhoneNumber
                          : localization.onboarding_mobilePhoneNumberTitle,
                  style: textStyles.textXl,
                  color: theme.text.textPrimary,
                  fontWeight: DuploFontWeight.semiBold,
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 8, bottom: 24),
                  child: DuploText(
                    text:
                        isUpdatingPhoneNumber
                            ? localization.hub_credentialPageSubtext
                            : localization.onboarding_mobilePhoneNumberSubtitle,
                    style: textStyles.textSm,
                    textAlign: TextAlign.start,
                    color: theme.text.textSecondary,
                    fontWeight: DuploFontWeight.regular,
                  ),
                ),
                Container(
                  child: DuploPhoneTextField(
                    key: const Key('mobile_input_field'),
                    semanticsIdentifier: 'mobile_input_field',
                    controller: phoneNumberController,
                    label: localization.onboarding_phoneNumber,
                    autoFocus: true,
                    forceTextDirection: TextDirection.ltr,
                    textAlign: isLtr ? TextAlign.left : TextAlign.right,
                    hint: state.placeholderText,
                    selectedCountryCode: state.selectedCountryCode,
                    selectedCountryFlag: state.selectedCountryFlag,
                    errorMessage: _getErrorMessage(
                      state.errorMessage,
                      localization,
                    ),
                    onSelectCountryCode:
                        () => _onCountryCodeSelectorPressed(
                          bloc: bloc,
                          context: context,
                        ),
                    onPhoneChanged: (formattedNumber) {
                      bloc.add(
                        MobileNumberInputEvent.phoneNumberChanged(
                          phoneNumber: formattedNumber,
                        ),
                      );
                    },
                  ),
                ),
                if (!isUpdatingPhoneNumber)
                  Padding(
                    padding: const EdgeInsets.only(top: 6, bottom: 24),
                    child: DuploText(
                      text: localization.onboarding_consentMarketingText,
                      style: textStyles.textXs,
                      textAlign: isLtr ? TextAlign.left : TextAlign.right,
                      color: theme.text.textTertiary,
                      fontWeight: DuploFontWeight.regular,
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _onCountryCodeSelectorPressed({
    required MobileNumberInputBloc bloc,
    required BuildContext context,
  }) {
    final localization = EquitiLocalization.of(context);
    DuploDropDown.customBottomSheetSelector(
      context: context,
      bottomSheetTitle: localization.onboarding_selectCountry,
      items:
          bloc.state.countries
              .map(
                (e) => DropDownItemModel(
                  title: "${e.name} (${e.dialCode ?? ''})",
                  image: FlagProvider.getFlagFromCountryCode(e.code),
                ),
              )
              .toList(),
      selectedIndex: bloc.state.selectedCountryIndex,
      onChanged: (index) {
        if (index != -1) {
          bloc.add(
            MobileNumberInputEvent.countryCodeSelected(selectedIndex: index),
          );
          phoneNumberController.clear();
        }
      },
      selectorSemanticsIdentifier: "mobile_number_country_code",
    );
  }

  Future<void> showRedirectionSheet(BuildContext context) async {
    final styles = context.duploTextStyles;
    final theme = context.duploTheme;
    final localization = EquitiLocalization.of(context);
    await DuploSheet.showModalSheetV2<void>(
      context,
      swipeDismissible: false,
      barrierDismissible: false,
      content: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 32),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Assets.images.redirectionImage.svg(),
            SizedBox(height: 16),
            DuploText(
              text: localization.hub_redirection_title,
              style: styles.textXl,
              fontWeight: DuploFontWeight.semiBold,
              color: theme.text.textPrimary,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16),
            DuploText(
              text: localization.hub_redirection_subtitle,
              style: styles.textSm,
              color: theme.text.textSecondary,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _onContinuePressed(BuildContext blocContext) {
    final bloc = blocContext.read<MobileNumberInputBloc>();
    final state = bloc.state;
    if (!state.isConfirmButtonEnabled) {
      return;
    }

    if (state.updatePhoneArgs?.isUpdatingPhoneNumber ?? false) {
      showRedirectionSheet(blocContext);
      Future<void>.delayed(const Duration(seconds: 2)).then((_) {
        Navigator.pop(blocContext);
        bloc.add(MobileNumberInputEvent.onUpdateButtonPressed());
      });
    } else {
      bloc.add(MobileNumberInputEvent.confirmButtonPressed());
    }
  }

  String? _getErrorMessage(
    PersonalDetailsError? error,
    EquitiLocalization localization,
  ) {
    if (error == null) return null;

    switch (error) {
      case PersonalDetailsError.invalidPhoneNumber:
        return localization.onboarding_errorInvalidPhoneNumber;
      case PersonalDetailsError.duplicatePhoneNumber:
        return localization.onboarding_errorDuplicatePhoneNumber;
      case PersonalDetailsError.unknownError:
        return localization.onboarding_errorUnknownError;
      case PersonalDetailsError.generalError:
        return localization.onboarding_errorGeneralError;
    }
  }
}
