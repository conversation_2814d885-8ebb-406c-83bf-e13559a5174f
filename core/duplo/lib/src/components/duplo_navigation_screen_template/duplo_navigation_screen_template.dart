import 'package:duplo/src/components/bottom_nav_bar/dublo_bottom_navbar_items.dart';
import 'package:duplo/src/components/bottom_nav_bar/duplo_bottom_navbar.dart';
import 'package:duplo/src/components/draggable_handle.dart';
import 'package:duplo/src/theming/duplo_theme_context_extension.dart';
import 'package:duplo/src/theming/duplo_theme_data.dart';
import 'package:flutter/material.dart';
import 'package:smooth_sheets/smooth_sheets.dart';

// ============================================================================
// CONFIGURATION - Defines what content to show
// ============================================================================

/// Configuration for content widgets positioned above the sheet
class DuploNavContentConfig {
  /// Progress bar widget (optional)
  final Widget? progressBar;

  /// Summary/info widget (required)
  final Widget summary;

  /// Actions widget (optional - funding buttons, reset button, etc.)
  final Widget? actions;

  /// Spacing after header before progress bar
  final double headerSpacing;

  /// Spacing between progress bar and summary
  final double progressToSummarySpacing;

  /// Spacing between summary and actions
  final double summaryToActionsSpacing;

  /// Optional explicit height for progress bar (if provided, overrides measurement)
  final double? progressBarHeight;

  /// Optional explicit height for summary widget (if provided, overrides measurement)
  final double? summaryHeight;

  /// Optional explicit height for actions widget (if provided, overrides measurement)
  final double? actionsHeight;

  const DuploNavContentConfig({
    this.progressBar,
    required this.summary,
    this.actions,
    this.headerSpacing = 8.0,
    this.progressToSummarySpacing = 8.0,
    this.summaryToActionsSpacing = 16.0,
    this.progressBarHeight,
    this.summaryHeight,
    this.actionsHeight,
  });
}

// ============================================================================
// LAYOUT CALCULATOR - Handles all positioning logic
// ============================================================================

class _DuploNavLayoutCalculator {
  final BuildContext context;
  final BoxConstraints constraints;
  final DuploNavContentConfig config;
  final double headerHeight;

  // Cached measurements
  late final double progressBarHeight;
  late final double summaryHeight;
  late final double actionsHeight;

  _DuploNavLayoutCalculator({
    required this.context,
    required this.constraints,
    required this.config,
    required this.headerHeight,
  }) {
    _calculateHeights();
  }

  void _calculateHeights() {
    // Use explicit heights if provided, otherwise measure widgets
    progressBarHeight =
        config.progressBarHeight ??
        (config.progressBar != null
            ? _measureWidget(config.progressBar!)
            : 0.0);

    summaryHeight = config.summaryHeight ?? _measureWidget(config.summary);

    actionsHeight =
        config.actionsHeight ??
        (config.actions != null ? _measureWidget(config.actions!) : 0.0);
  }

  /// Measures a widget's height (simplified - in production use RenderBox)
  double _measureWidget(Widget widget) {
    // Check for SizedBox with explicit height
    if (widget is SizedBox && widget.height != null) {
      return widget.height!;
    }
    // Check for Container with explicit height
    if (widget is Container && widget.constraints != null) {
      if (widget.constraints!.hasBoundedHeight) {
        return widget.constraints!.maxHeight;
      }
    }
    // Default fallback - should be overridden with explicit heights for accuracy
    return 50.0;
  }

  // Position calculations
  double get progressBarTop => headerHeight + config.headerSpacing;

  double get summaryTop {
    if (config.progressBar != null) {
      return progressBarTop +
          progressBarHeight +
          config.progressToSummarySpacing;
    }
    return headerHeight + config.headerSpacing;
  }

  double get actionsTop {
    return summaryTop + summaryHeight + config.summaryToActionsSpacing;
  }

  /// Whether actions widget should be shown
  bool get shouldShowActions => config.actions != null;

  // Content height calculation
  double get totalContentHeight {
    if (shouldShowActions) {
      return actionsTop + actionsHeight;
    }
    return summaryTop + summaryHeight + config.summaryToActionsSpacing;
  }

  // Sheet offset calculations
  double get initialSheetOffset {
    final screenHeight = constraints.maxHeight;
    final sheetViewport = screenHeight - headerHeight;
    final contentHeight = totalContentHeight - headerHeight;
    final remainingSpace = sheetViewport - contentHeight;
    // Convert to percentage of viewport
    final offset = remainingSpace / sheetViewport;
    return offset.clamp(0.2, 1.0);
  }

  double get maxSheetOffset {
    final screenHeight = constraints.maxHeight;
    final sheetViewport = screenHeight - headerHeight;
    final minTopSpace = config.headerSpacing + progressBarHeight;
    return ((sheetViewport - minTopSpace) / sheetViewport).clamp(0.2, 1.0);
  }
}

// ============================================================================
// MAIN TEMPLATE
// ============================================================================

/// A reusable navigation screen template with bottom sheet and bottom nav bar.
///
/// **Features:**
/// - Custom header (AppBar)
/// - Optional progress bar above content
/// - Required summary/info widget
/// - Optional actions widget (shows/hides dynamically)
/// - Draggable bottom sheet
/// - Bottom navigation bar
///
/// **Usage:**
/// ```dart
/// DuploNavigationScreenTemplate(
///   header: MyAppBar(),
///   contentConfig: DuploNavContentConfig(
///     progressBar: MyProgressBar(),
///     summary: MySummaryWidget(),
///     actions: shouldShowActions ? MyActionsWidget() : null,
///   ),
///   sheetContent: MySheetContent(),
///   navItems: [...],
///   onTabChanged: (index) => ...,
/// )
/// ```
class DuploNavigationScreenTemplate extends StatefulWidget {
  const DuploNavigationScreenTemplate({
    super.key,
    required this.header,
    required this.contentConfig,
    required this.sheetContent,
    required this.navItems,
    this.initialTabIndex = 0,
    this.onTabChanged,
    this.sheetController,
    this.backgroundColor,
  });

  /// Header widget (typically an AppBar)
  final PreferredSizeWidget header;

  /// Configuration for content above the sheet
  final DuploNavContentConfig contentConfig;

  /// Content widget inside the draggable sheet
  final Widget sheetContent;

  /// Bottom navigation bar items
  final List<DubloBottomNavBarItems> navItems;

  /// Initial selected tab index
  final int initialTabIndex;

  /// Callback when tab changes
  final void Function(int)? onTabChanged;

  /// Optional sheet controller for programmatic control
  final SheetController? sheetController;

  /// Background color (defaults to theme background)
  final Color? backgroundColor;

  @override
  State<DuploNavigationScreenTemplate> createState() =>
      _DuploNavigationScreenTemplateState();
}

class _DuploNavigationScreenTemplateState
    extends State<DuploNavigationScreenTemplate> {
  late final ValueNotifier<int> _navController;
  late final SheetController _draggableController;

  // Cache layout calculator - recalculates on size or config change
  _DuploNavLayoutCalculator? _cachedLayout;
  Size? _lastConstraintsSize;
  DuploNavContentConfig? _lastContentConfig;

  // Track state for sheet adjustment
  bool? _lastShouldShowActions;
  int? _lastTabIndex;
  static const double _sheetExpansionThreshold = 0.9;

  // Track if user has manually expanded the sheet
  bool _userHasExpandedSheet = false;

  @override
  void initState() {
    super.initState();
    _draggableController = widget.sheetController ?? SheetController();
    _navController = ValueNotifier<int>(widget.initialTabIndex);
    _lastTabIndex = widget.initialTabIndex;
    _lastShouldShowActions = widget.contentConfig.actions != null;
    _navController.addListener(_onTabIndexChanged);
    _draggableController.addListener(_onSheetPositionChanged);
  }

  @override
  void didUpdateWidget(DuploNavigationScreenTemplate oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Sync navigation controller if initialTabIndex changed externally
    if (oldWidget.initialTabIndex != widget.initialTabIndex) {
      _navController.value = widget.initialTabIndex;
    }
  }

  void _onTabIndexChanged() {
    widget.onTabChanged?.call(_navController.value);
  }

  void _onSheetPositionChanged() {
    // Track if user has manually expanded the sheet beyond the threshold
    if (_isSheetExpanded()) {
      _userHasExpandedSheet = true;
    }
  }

  @override
  void dispose() {
    _navController.removeListener(_onTabIndexChanged);
    _draggableController.removeListener(_onSheetPositionChanged);
    _navController.dispose();
    if (widget.sheetController == null) {
      _draggableController.dispose();
    }
    super.dispose();
  }

  /// Checks if the sheet is currently expanded (user has dragged it up)
  bool _isSheetExpanded() {
    final metrics = _draggableController.metrics;
    if (metrics == null) return false;

    final currentOffset = metrics.offset;
    final maxOffset = metrics.maxOffset;

    // Consider the sheet expanded if it's at 90% or more of max offset
    return currentOffset >= (maxOffset * _sheetExpansionThreshold);
  }

  /// Gets or calculates layout (caches based on size and config)
  _DuploNavLayoutCalculator _getLayout({
    required BuildContext context,
    required BoxConstraints constraints,
  }) {
    final currentSize = Size(constraints.maxWidth, constraints.maxHeight);
    final currentConfig = widget.contentConfig;
    final shouldShowActions = currentConfig.actions != null;

    // Check if we need to recalculate
    final sizeChanged = _lastConstraintsSize != currentSize;
    final configChanged = _lastContentConfig != currentConfig;
    final actionsVisibilityChanged =
        _lastShouldShowActions != shouldShowActions;

    if (_cachedLayout != null &&
        !sizeChanged &&
        !configChanged &&
        !actionsVisibilityChanged) {
      return _cachedLayout!;
    }

    final layout = _DuploNavLayoutCalculator(
      context: context,
      constraints: constraints,
      config: currentConfig,
      headerHeight: widget.header.preferredSize.height,
    );

    _cachedLayout = layout;
    _lastConstraintsSize = currentSize;
    _lastContentConfig = currentConfig;
    _lastShouldShowActions = shouldShowActions;

    return layout;
  }

  /// Adjusts sheet position when content config changes (actions visibility, tab changes, etc.)
  /// Only adjusts if sheet is NOT expanded to preserve user's manual positioning
  void _adjustSheetForContentChange(
    double newInitialOffset,
    bool actionsVisibilityChanged,
  ) {
    // Don't adjust if user has manually expanded the sheet
    if (_userHasExpandedSheet || _isSheetExpanded()) {
      return;
    }

    final currentTabIndex = _navController.value;
    final tabChanged =
        _lastTabIndex != null && _lastTabIndex != currentTabIndex;

    // Only animate if tab changed or actions visibility changed
    if (tabChanged || actionsVisibilityChanged) {
      _lastTabIndex = currentTabIndex;
      _lastShouldShowActions = widget.contentConfig.actions != null;

      // Schedule animation after build completes for smooth transition
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && !_userHasExpandedSheet && !_isSheetExpanded()) {
          _draggableController.animateTo(
            SheetOffset(newInitialOffset),
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
      });
    } else {
      // Update tracking even if we don't animate (for first build)
      _lastTabIndex = currentTabIndex;
    }
  }

  void _handleBottomNavTabChange(int index) {
    _navController.value = index;
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final backgroundColor =
        widget.backgroundColor ?? theme.background.bgPrimary;

    return Scaffold(
      backgroundColor: backgroundColor,
      bottomNavigationBar: DuploBottomNavbar(
        onNavBarCallBack: _handleBottomNavTabChange,
        controller: _navController,
        navItems: widget.navItems,
      ),
      body: SafeArea(
        bottom: false,
        child: LayoutBuilder(
          builder: (layoutContext, constraints) {
            final currentShouldShowActions =
                widget.contentConfig.actions != null;
            final actionsVisibilityChanged =
                _lastShouldShowActions != currentShouldShowActions;

            final layout = _getLayout(
              context: layoutContext,
              constraints: constraints,
            );

            // Adjust sheet when content config changes
            _adjustSheetForContentChange(
              layout.initialSheetOffset,
              actionsVisibilityChanged,
            );

            return ColoredBox(
              color: backgroundColor,
              child: Stack(
                children: [
                  // Header
                  Positioned(top: 0, left: 0, right: 0, child: widget.header),

                  // Progress bar (optional)
                  if (widget.contentConfig.progressBar != null)
                    Positioned(
                      top: layout.progressBarTop,
                      left: 0,
                      right: 0,
                      child: widget.contentConfig.progressBar!,
                    ),

                  // Summary widget
                  Positioned(
                    top: layout.summaryTop,
                    left: 0,
                    right: 0,
                    child: widget.contentConfig.summary,
                  ),

                  // Actions widget (optional)
                  if (widget.contentConfig.actions != null)
                    Positioned(
                      top: layout.actionsTop,
                      left: 0,
                      right: 0,
                      child: widget.contentConfig.actions!,
                    ),

                  // Draggable sheet
                  _buildSheet(layout, theme),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildSheet(_DuploNavLayoutCalculator layout, DuploThemeData theme) {
    return Positioned.fill(
      top: layout.headerHeight,
      child: SheetViewport(
        child: Sheet(
          controller: _draggableController,
          scrollConfiguration: const SheetScrollConfiguration(),
          decoration: MaterialSheetDecoration(
            size: SheetSize.stretch,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
            color: theme.background.bgSecondary,
          ),
          initialOffset: SheetOffset(layout.initialSheetOffset),
          physics: const BouncingSheetPhysics(),
          snapGrid: SheetSnapGrid(
            snaps: [
              SheetOffset(layout.initialSheetOffset),
              SheetOffset(layout.maxSheetOffset),
            ],
          ),
          child: DecoratedBox(
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(24),
              ),
              color: theme.background.bgSecondary,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.08),
                  blurRadius: 4,
                  offset: const Offset(0, -1),
                ),
              ],
            ),
            child: SafeArea(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const SizedBox(height: 8),
                  const DraggableHandle(),
                  Expanded(child: widget.sheetContent),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
