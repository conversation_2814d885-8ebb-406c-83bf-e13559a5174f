import 'package:flutter_test/flutter_test.dart';

Future<void> iTapIdentifier(WidgetTester tester, String identifier) async {
  // Wait for the widget tree to settle
  await tester.pumpAndSettle();

  final finder = find.bySemanticsIdentifier(identifier);

  // Ensure the widget exists before tapping
  expect(
    finder,
    findsOneWidget,
    reason: 'Could not find widget with identifier: $identifier',
  );

  await tester.tap(finder);
  await tester.pumpAndSettle();
}
