import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';

/// Usage: I enter {"123456"} in the field with identifier {"current_password"}
Future<void> iEnterInTheFieldWithIdentifier(
  WidgetTester tester,
  String text,
  String identifier,
) async {
  // First try to find DuploText<PERSON>ield with the given identifier
  final finder = find.bySemanticsIdentifier(identifier);

  expect(
    finder,
    findsOneWidget,
    reason: 'Could not find DuploTextField with identifier: $identifier',
  );

  // Find the TextFormField inside the DuploTextField
  final textFormFieldFinder = find.descendant(
    of: finder,
    matching: find.byType(TextFormField),
  );

  expect(
    textFormFieldFinder,
    findsOneWidget,
    reason:
        'Could not find Text<PERSON><PERSON><PERSON><PERSON> inside field with identifier: $identifier',
  );

  // Tap on the text field to focus it
  await tester.tap(textFormFieldFinder);
  await tester.pump();

  // Enter the text
  await tester.enterText(textForm<PERSON>ieldFinder, text);
  await tester.pump();
}
