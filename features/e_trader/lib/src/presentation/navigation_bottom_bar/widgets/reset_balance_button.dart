import 'package:duplo/duplo.dart';
import 'package:e_trader/src/presentation/reset_balance/reset_balance_screen.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';

class ResetBalanceButton extends StatelessWidget {
  const ResetBalanceButton({
    super.key,
    required this.accountNumber,
    required this.homeCurrency,
    this.topPadding = 10,
  });

  final String accountNumber;
  final String homeCurrency;
  final double topPadding;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final textStyle = context.duploTextStyles;
    final localization = EquitiLocalization.of(context);
    return Padding(
      padding: EdgeInsets.fromLTRB(16, topPadding, 16, 10),
      child: DuploButton.secondary(
        title: localization.trader_reset_balance,
        textStyle: textStyle.textXs,
        borderThickness: 1,
        onTap: () {
          DuploSheet.showModalSheetV2<void>(
            context,
            content: ResetBalanceScreen(
              accountNumber: accountNumber,
              accountCurrency: homeCurrency,
            ),
          );
        },
        leadingIcon: Assets.images.creditCardUpload.keyName,
        iconColor: theme.button.buttonSecondaryFg,
      ),
    );
  }
}
