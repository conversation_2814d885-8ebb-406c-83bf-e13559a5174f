import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/account/account_screen.dart';
import 'package:e_trader/src/presentation/discover/discover_screen.dart';
import 'package:e_trader/src/presentation/performance_screen/performance_screen.dart';
import 'package:e_trader/src/presentation/portfolio/orders/bloc/orders_bloc.dart';
import 'package:e_trader/src/presentation/portfolio/positions/bloc/position_bloc.dart';
import 'package:e_trader/src/presentation/portfolio/positions/portfolio_position_screen.dart';
import 'package:e_trader/src/presentation/price_alert/active_price_alerts/bloc/active_price_alerts_bloc.dart';
import 'package:e_trader/src/presentation/symbols/bloc/categories/categories_bloc.dart';
import 'package:e_trader/src/presentation/symbols/symbols_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:lazy_load_indexed_stack/lazy_load_indexed_stack.dart';

class TabContentWidget extends StatelessWidget {
  const TabContentWidget({
    required this.tabIndex,
    required this.portfolioTabListIndex,
    required this.accountSettingsTab,
  });

  final int tabIndex;
  final ValueNotifier<int> portfolioTabListIndex;
  final int accountSettingsTab;

  @override
  Widget build(BuildContext context) {
    return LazyLoadIndexedStack(
      index: tabIndex,
      children: [
        // NavigationTab.discover
        DiscoverScreen(),
        SymbolsScreen(),
        MultiBlocProvider(
          providers: [
            BlocProvider(
              create: (_) => diContainer<PositionBloc>(param1: ''),
            ),
            BlocProvider(create: (_) => diContainer<OrdersBloc>(param1: '')),
            BlocProvider(
              create:
                  (_) =>
                      diContainer<CategoriesBloc>()
                        ..add(CategoriesEvent.onGetCategories()),
            ),
            BlocProvider(create: (_) => diContainer<ActivePriceAlertsBloc>()),
          ],
          child: ValueListenableBuilder<int>(
            valueListenable: portfolioTabListIndex,
            builder:
                (_, pIndex, __) => PortfolioPositionScreen(
                  key: ValueKey('$pIndex'),
                  tabIndex: pIndex,
                ),
          ),
        ),
        PerformanceScreen(),
        AccountScreen(tabIndex: accountSettingsTab),
      ],
    );
  }
}
