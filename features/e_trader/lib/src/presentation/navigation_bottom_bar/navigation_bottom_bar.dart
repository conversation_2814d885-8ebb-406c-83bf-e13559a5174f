import 'package:domain/domain.dart';
import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/di/trading_environment_dependencies.dart';
import 'package:e_trader/src/domain/usecase/get_selected_account_use_case.dart';
import 'package:e_trader/src/domain/usecase/update_trading_account_balance_hub_use_case.dart';
import 'package:e_trader/src/navigation/equiti_trader_route_schema.dart';
import 'package:e_trader/src/presentation/model/trade_confirmation_result.dart';
import 'package:e_trader/src/presentation/navigation_bottom_bar/account_balance/bloc/account_balance_bloc.dart';
import 'package:e_trader/src/presentation/navigation_bottom_bar/bloc/navigation_bottom_bar_bloc.dart';
import 'package:e_trader/src/presentation/navigation_bottom_bar/enums/navigation_tab.dart';
import 'package:e_trader/src/presentation/navigation_bottom_bar/widgets/reset_balance_button.dart';
import 'package:e_trader/src/presentation/navigation_bottom_bar/widgets/tab_content_widget.dart';
import 'package:e_trader/src/presentation/symbols/widgets/tab_subscription_registry.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:equiti_router/equiti_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:monitoring/monitoring.dart';
import 'package:prelude/prelude.dart';
import 'package:smooth_sheets/smooth_sheets.dart';

import 'package:duplo/src/components/account_header_widget.dart' as duplo;
import 'package:e_trader/src/navigation/equiti_trader_navigation.dart';
import 'package:e_trader/src/presentation/duplo/company_logo_widget.dart';
import 'package:e_trader/src/presentation/navigation_bottom_bar/widgets/account_status_line.dart';
import 'package:e_trader/src/presentation/navigation_bottom_bar/widgets/equity_or_nickname.dart';

part 'widgets/account_summary_content_widget.dart';

// ============================================================================
// MAIN WIDGET
// ============================================================================

class NavigationBottomBar extends StatefulWidget {
  const NavigationBottomBar({super.key});

  @override
  State<NavigationBottomBar> createState() => _NavigationBottomBarState();
}

class _NavigationBottomBarState extends State<NavigationBottomBar> {
  late Future<void> _dependencyRegistrationFuture;

  @override
  void initState() {
    super.initState();
    final selectedAccount = diContainer<GetSelectedAccountUseCase>().call();
    final isDemo = selectedAccount?.isDemo ?? false;
    _dependencyRegistrationFuture = TradingEnvironmentDependencies.register(
      isDemo: isDemo,
    );
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<void>(
      future: _dependencyRegistrationFuture,
      builder: (builderContext, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        if (snapshot.hasError) {
          return Scaffold(
            body: Center(
              child: Text(
                'Error initializing: ${snapshot.error ?? 'Unknown error'}',
              ),
            ),
          );
        }

        return MultiBlocProvider(
          providers: [
            BlocProvider(
              create:
                  (_) =>
                      diContainer<NavigationBottomBarBloc>()
                        ..add(NavigationBottomBarEvent.fetchTradingAccounts()),
            ),
            BlocProvider(create: (_) => diContainer<AccountBalanceBloc>()),
          ],
          child: BlocBuilder<NavigationBottomBarBloc, NavigationBottomBarState>(
            buildWhen: (previous, current) => previous != current,
            builder: (_, state) {
              if (state.hasOpenPositions == HasOpenPositions.checking) {
                return LoadingView();
              }
              return _NavigationBarContent(
                initialTab:
                    state.hasOpenPositions == HasOpenPositions.yes
                        ? NavigationTab.portfolio
                        : NavigationTab.markets,
              );
            },
          ),
        );
      },
    );
  }
}

// ============================================================================
// CONTENT WIDGET
// ============================================================================

class _NavigationBarContent extends StatefulWidget {
  const _NavigationBarContent({required this.initialTab});
  final NavigationTab initialTab;

  @override
  State<_NavigationBarContent> createState() => _NavigationBarContentState();
}

class _NavigationBarContentState extends State<_NavigationBarContent>
    with RouteAwareAppLifecycleMixin {
  late final ValueNotifier<int> _navController;
  late final ValueNotifier<int> _portfolioTabIndex;
  late final SheetController _draggableController;

  int accountSettingsTab = 0;
  NavigationTab _prevTab = NavigationTab.markets;

  NavigationTab get _currentTab =>
      NavigationTab.values.elementAtOrNull(_navController.value) ??
      NavigationTab.markets;

  set _currentTab(NavigationTab tab) => _navController.value = tab.index;

  @override
  void initState() {
    super.initState();
    _draggableController = SheetController();
    _navController = ValueNotifier<int>(widget.initialTab.index);
    _portfolioTabIndex = ValueNotifier<int>(0);
    _prevTab = _currentTab;
    _navController.addListener(_onTabIndexChanged);
  }

  void _onTabIndexChanged() {
    final nextTab = _currentTab;
    if (nextTab == _prevTab) return;

    context.read<NavigationBottomBarBloc>().add(
      NavigationBottomBarEvent.changeTab(tab: nextTab),
    );

    _handleTabSubscriptions(_prevTab, nextTab);
    _prevTab = nextTab;
  }

  void _handleTabSubscriptions(NavigationTab from, NavigationTab to) {
    if (from == NavigationTab.markets) {
      TabSubscriptionRegistry.pauseSymbolsTab();
    } else if (from == NavigationTab.portfolio) {
      TabSubscriptionRegistry.pausePortfolioTab();
    }

    if (to == NavigationTab.markets) {
      TabSubscriptionRegistry.resumeSymbolsTab();
    } else if (to == NavigationTab.portfolio) {
      TabSubscriptionRegistry.resumePortfolioTab();
    }
  }

  @override
  void dispose() {
    _navController.removeListener(_onTabIndexChanged);
    _navController.dispose();
    _portfolioTabIndex.dispose();
    _draggableController.dispose();
    super.dispose();
  }

  bool _shouldShowActions(NavigationBottomBarState state) {
    return !state.tradingAccountModel.isDemo ||
        (state.tab == NavigationTab.account &&
            state.tradingAccountModel.isDemo);
  }

  /// Calculates the heights for content widgets using the same logic as the original implementation
  ({double progressBarHeight, double summaryHeight, double actionsHeight})
  _calculateContentHeights(
    BuildContext context,
    NavigationBottomBarState state,
  ) {
    // Calculate progress bar height
    final marginLevel = state.tradingAccountModel.marginLevel ?? 0;
    final progressBarHeight =
        DuploMarginProgressBar(
          height: 4,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          marginLevel: marginLevel,
        ).getHeight();

    // Calculate summary height using text measurements
    final textStyles = context.duploTextStyles;
    final textXsHeight =
        measureText(
          context: context,
          text: '',
          textStyle: textStyles.textXs.toTextStyle(),
        ).height;
    final displaySmHeight =
        measureText(
          context: context,
          text: '',
          textStyle: textStyles.displaySm.toTextStyle(),
        ).height;
    final textSmHeight =
        measureText(
          context: context,
          text: '',
          textStyle: textStyles.textSm.toTextStyle(),
        ).height;

    final summaryHeight =
        25 + // padding
        textXsHeight +
        displaySmHeight +
        textSmHeight;

    // Actions height
    const actionsHeight = 60.0;

    return (
      progressBarHeight: progressBarHeight,
      summaryHeight: summaryHeight,
      actionsHeight: actionsHeight,
    );
  }

  Widget _buildIcon(trader.SvgGenImage iconAsset, Color color) {
    return iconAsset.svg(colorFilter: ColorFilter.mode(color, BlendMode.srcIn));
  }

  List<DubloBottomNavBarItems> _buildNavItems(BuildContext context) {
    final localization = EquitiLocalization.of(context);
    final theme = context.duploTheme;
    final selectedColor = theme.foreground.fgWhite;
    final unselectedColor = theme.foreground.fgDisabled;

    return NavigationTab.values.map((tab) {
      final String title;
      final trader.SvgGenImage icon;

      switch (tab) {
        case NavigationTab.discover:
          title = localization.trader_discover;
          icon = trader.Assets.images.discoverIcon;
        case NavigationTab.markets:
          title = localization.trader_markets;
          icon = trader.Assets.images.marketsIcon;
        case NavigationTab.portfolio:
          title = localization.trader_portfolio;
          icon = trader.Assets.images.portfolioIcon;
        case NavigationTab.performance:
          title = localization.trader_performance;
          icon = trader.Assets.images.performanceIcon;
        case NavigationTab.account:
          title = localization.trader_account;
          icon = trader.Assets.images.account;
      }

      return DubloBottomNavBarItems(
        title: title,
        selectedIcon: _buildIcon(icon, selectedColor),
        unselectedIcon: _buildIcon(icon, unselectedColor),
      );
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<NavigationBottomBarBloc, NavigationBottomBarState>(
      buildWhen:
          (previous, current) =>
              previous.tab != current.tab ||
              previous.tradingAccountModel != current.tradingAccountModel,
      builder: (builderContext, state) {
        return RouteVisibilityInheritedWidget(
          isRouteVisible: isRouteVisible,
          currentAppState: currentAppState,
          child: BlocSelector<
            NavigationBottomBarBloc,
            NavigationBottomBarState,
            TradingAccountModel
          >(
            selector: (s) => s.tradingAccountModel,
            builder: (_, tradingAccount) {
              final contentHeights = _calculateContentHeights(
                builderContext,
                state,
              );

              return DuploNavigationScreenTemplate(
                key: ValueKey('nav_template_${_navController.value}'),
                header: _buildHeader(tradingAccount),
                contentConfig: DuploNavContentConfig(
                  progressBar: _buildProgressBar(),
                  summary: _buildSummary(),
                  actions:
                      _shouldShowActions(state)
                          ? _buildActions(context: builderContext, state: state)
                          : null,
                  progressBarHeight: contentHeights.progressBarHeight,
                  summaryHeight: contentHeights.summaryHeight,
                  actionsHeight: contentHeights.actionsHeight,
                ),
                sheetContent: ValueListenableBuilder<int>(
                  valueListenable: _navController,
                  builder:
                      (_, navIndex, __) => TabContentWidget(
                        tabIndex: navIndex,
                        portfolioTabListIndex: _portfolioTabIndex,
                        accountSettingsTab: accountSettingsTab,
                      ),
                ),
                navItems: _buildNavItems(context),
                initialTabIndex: _navController.value,
                onTabChanged: (index) {
                  // Update our controller, which will trigger _onTabIndexChanged
                  _navController.value = index;
                },
                sheetController: _draggableController,
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildProgressBar() {
    return BlocBuilder<AccountBalanceBloc, AccountBalanceState>(
      buildWhen:
          (previous, current) =>
              previous.tradingAccountViewModel.marginLevel !=
                  current.tradingAccountViewModel.marginLevel ||
              previous.tradingAccountViewModel.equity !=
                  current.tradingAccountViewModel.equity,
      builder: (context, accountState) {
        return ListenableBuilder(
          listenable: accountState.tradingAccountViewModel,
          builder: (_, __) {
            return DuploMarginProgressBar(
              height: 4,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              marginLevel:
                  accountState.tradingAccountViewModel.marginLevel ?? 0,
              balance: accountState.tradingAccountViewModel.equity,
            );
          },
        );
      },
    );
  }

  Widget _buildSummary() {
    return const Padding(
      padding: EdgeInsetsDirectional.fromSTEB(16, 16, 16, 40),
      child: _AccountSummaryContentWidget(),
    );
  }

  Widget _buildActions({
    required BuildContext context,
    required NavigationBottomBarState state,
  }) {
    if (!state.tradingAccountModel.isDemo) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: DuploFundingButtons(
          onDepositPressed: () {
            context.read<NavigationBottomBarBloc>().add(
              const NavigationBottomBarEvent.navigateToDepositPaymentOptions(),
            );
          },
          onTransferPressed: () {
            context.read<NavigationBottomBarBloc>().add(
              const NavigationBottomBarEvent.navigateToTransferFunds(),
            );
          },
          onWithdrawPressed: () {
            context.read<NavigationBottomBarBloc>().add(
              const NavigationBottomBarEvent.navigateToWithdrawPaymentOptions(),
            );
          },
        ),
      );
    } else {
      return ResetBalanceButton(
        accountNumber: state.tradingAccountModel.accountNumber,
        homeCurrency: state.tradingAccountModel.homeCurrency,
        topPadding: 0, // remove extra padding ,
      );
    }
  }

  PreferredSizeWidget _buildHeader(TradingAccountModel tradingAccount) {
    final accountStatusLineData = AccountStatusLineData(
      isDemo: tradingAccount.isDemo,
      nickName: tradingAccount.nickName,
      platformTypeName: tradingAccount.platformTypeName,
      homeCurrency: tradingAccount.homeCurrency,
      accountNumber: tradingAccount.accountNumber,
    );

    return duplo.AccountHeaderWidget(
      sheetController: _draggableController,
      accountStatusLineData: accountStatusLineData,
      onSettingsTap:
          () => diContainer<EquitiTraderNavigation>().navigateToHubSettings(),
      onSwitchAccountsTap:
          () =>
              diContainer<EquitiTraderNavigation>().navigateToSwitchAccounts(),
      accountStatusLineBuilder: (data, sheetExpanded) {
        return AccountStatusLine(
          accountStatusLineData: data,
          sheetExpanded: sheetExpanded,
        );
      },
      equityOrNicknameBuilder: (sheetExpanded) {
        return EquityOrNickname(sheetExpanded: sheetExpanded);
      },
      companyLogoWidget: CompanyLogoWidget(
        platformType: tradingAccount.platformType,
        useMono: true,
      ),
    );
  }

  @override
  void onRoutePopped(Route<Object?> route) {
    if (route.settings.name != 'confirmation_sheet') return;

    final result =
        diContainer<EquitiNavigatorBase>().globalData[EquitiTraderRouteSchema
            .navBarRoute
            .url];

    switch (result) {
      case TradeConfirmationResult.viewTrades:
        _portfolioTabIndex.value = 1;
        _currentTab = NavigationTab.portfolio;
      case TradeConfirmationResult.viewOrders:
        _portfolioTabIndex.value = 2;
        _currentTab = NavigationTab.portfolio;
      default:
        _currentTab = NavigationTab.markets;
    }

    diContainer<EquitiNavigatorBase>().globalData.remove(
      EquitiTraderRouteSchema.navBarRoute.url,
    );
  }
}
