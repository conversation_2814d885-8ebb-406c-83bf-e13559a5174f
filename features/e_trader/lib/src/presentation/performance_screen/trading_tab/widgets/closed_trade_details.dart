import 'package:customer_support_chat/customer_support_chat.dart';
import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/api/trading_response_model.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/performance_screen/trading_tab/bloc/trading_bloc.dart';
import 'package:e_trader/src/presentation/performance_screen/trading_tab/widgets/closed_trade_details_shimmer.dart';
import 'package:e_trader/src/presentation/performance_screen/trading_tab/widgets/trading_tile.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:locale_manager/locale_manager.dart';
import 'package:monitoring/monitoring.dart';
import 'package:prelude/prelude.dart';
import 'package:theme_manager/theme_manager.dart';

class ClosedTradeDetails extends StatelessWidget {
  final TradingListModel tradingItem;

  const ClosedTradeDetails({super.key, required this.tradingItem});

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final style = context.duploTextStyles;
    final loc = EquitiLocalization.of(context);

    return Scaffold(
      backgroundColor: theme.background.bgSecondary,
      body: BlocProvider<TradingBloc>(
        create:
            (_) =>
                diContainer<TradingBloc>()
                  ..add(TradingEvent.getTrades())
                  ..add(TradingEvent.getTradeReceipt(dealId: tradingItem.id)),
        child: BlocConsumer<TradingBloc, TradingState>(
          listenWhen:
              (previous, current) =>
                  previous.tradingProcessState != current.tradingProcessState,
          listener: (listenerContext, state) {
            if (state.tradingProcessState is TradingProcessStateError) {
              final toast = DuploToast();
              toast.hidesToastMessage();
              toast.showToastMessage(
                autoCloseDuration: const Duration(seconds: 4),
                context: listenerContext,
                widget: DuploToastMessage(
                  semanticsIdentifier:
                      state.errorCounter == 1
                          ? 'closed_trade_failed_to_load_toast_message'
                          : "closed_trade_conatct_support_team_toast_message",
                  titleMessage:
                      state.errorCounter == 1
                          ? loc.trader_failedToLoad
                          : loc.trader_contactSupportTeam,
                  descriptionMessage:
                      state.errorCounter == 1
                          ? loc.trader_failedToLoadDescription
                          : loc.trader_contactSupportDescription,
                  messageType: ToastMessageType.error,
                  onLeadingAction: () => toast.hidesToastMessage(),
                  actionButtonTitle:
                      state.errorCounter == 1
                          ? loc.trader_reload
                          : loc.trader_raiseSupportTicket,
                  onTap: () {
                    toast.hidesToastMessage();
                    listenerContext.read<TradingBloc>()
                      ..add(TradingEvent.getTrades())
                      ..add(
                        TradingEvent.getTradeReceipt(dealId: tradingItem.id),
                      );
                  },
                ),
              );
            }
          },
          buildWhen: (previous, current) => previous != current,
          builder: (builderContext, state) {
            double netProfit =
                (state.tradeReceiptDetails?.profit ??
                    tradingItem.tradeDetail.profit) +
                (state.tradeReceiptDetails?.commission ??
                    tradingItem.tradeDetail.commission) +
                (state.tradeReceiptDetails?.swap ?? 0.0);
            return switch (state.tradingProcessState) {
              TradingProcessStateLoading() => ClosedTradeDetailsShimmer(),
              TradingProcessStateSuccess() => SafeArea(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TradingTile(
                        tradingItem: tradingItem,
                        accountCurrency: state.accountCurrency ?? "",
                      ),
                      Container(
                        padding: const EdgeInsetsDirectional.only(
                          start: 16,
                          end: 16,
                          bottom: 12,
                        ),
                        decoration: BoxDecoration(
                          color: theme.background.bgPrimary,
                          border: BorderDirectional(
                            top: BorderSide(
                              color: theme.border.borderSecondary,
                            ),
                            start: BorderSide(
                              width: 4,
                              color:
                                  ((state
                                                  .tradeReceiptDetails
                                                  ?.tradeType
                                                  ?.isNotEmpty ??
                                              false)
                                          ? state
                                                  .tradeReceiptDetails
                                                  ?.tradeType ==
                                              "Buy"
                                          : tradingItem.tradeDetail.tradeType ==
                                              "Buy")
                                      ? theme.utility.utilitySuccess700
                                      : theme.foreground.fgErrorPrimary,
                            ),
                            end: BorderSide(
                              color: theme.border.borderSecondary,
                            ),
                            bottom: BorderSide(
                              color: theme.border.borderSecondary,
                            ),
                          ),
                        ),
                        child: Column(
                          children: [
                            Padding(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  DuploText(
                                    text: loc.trader_tradeClosedOn,
                                    style: style.textXs,
                                    color: theme.text.textTertiary,
                                  ),
                                  DuploText(
                                    text:
                                        EquitiFormatter.formatDateTimeWithSuffix(
                                          state.tradeReceiptDetails?.date,
                                          locale:
                                              Localizations.localeOf(
                                                context,
                                              ).toString(),
                                        ),
                                    style: style.textXs,
                                    color: theme.text.textPrimary,
                                  ),
                                ],
                              ),
                            ),
                            Divider(color: theme.border.borderSecondary),
                            Padding(
                              padding: const EdgeInsets.symmetric(vertical: 13),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      DuploText(
                                        text: loc.trader_swapTitle,
                                        style: style.textXs,
                                        color: theme.text.textTertiary,
                                      ),
                                      const SizedBox(height: 4),
                                      DuploText(
                                        text: loc.trader_commisionTitle,
                                        style: style.textXs,
                                        color: theme.text.textTertiary,
                                      ),
                                    ],
                                  ),
                                  Directionality(
                                    textDirection: TextDirection.ltr,
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.end,
                                      children: [
                                        DuploText(
                                          text:
                                              "${EquitiFormatter.decimalPatternDigits(value: state.tradeReceiptDetails?.swap ?? 0, digits: 2, locale: Localizations.localeOf(context).toString())} ${state.accountCurrency!}",
                                          style: style.textXs,
                                          color: theme.text.textPrimary,
                                        ),
                                        const SizedBox(height: 4),
                                        DuploText(
                                          text:
                                              "${EquitiFormatter.decimalPatternDigits(value: state.tradeReceiptDetails?.commission ?? tradingItem.tradeDetail.commission, digits: 2, locale: Localizations.localeOf(context).toString())} ${state.accountCurrency!}",
                                          style: style.textXs,
                                          color: theme.text.textPrimary,
                                        ),
                                      ],
                                    ),
                                  ),
                                  Container(
                                    color: theme.border.borderSecondary,
                                    height: 40,
                                    width: 1,
                                    margin: EdgeInsets.symmetric(horizontal: 5),
                                  ),
                                  Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      DuploText(
                                        text: loc.trader_netProfitTitle,
                                        style: style.textXs,
                                        color: theme.text.textTertiary,
                                      ),
                                      const SizedBox(height: 4),
                                      DuploText(
                                        text: loc.trader_grossProfitTitle,
                                        style: style.textXs,
                                        color: theme.text.textTertiary,
                                      ),
                                    ],
                                  ),
                                  Directionality(
                                    textDirection: TextDirection.ltr,
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.end,
                                      children: [
                                        DuploText(
                                          text:
                                              "${EquitiFormatter.formatTradeProfitOrLoss(value: netProfit, locale: Localizations.localeOf(context).toString())} ${state.accountCurrency!}",
                                          style: style.textXs,
                                          color: theme.text.textPrimary,
                                        ),
                                        const SizedBox(height: 4),
                                        DuploText(
                                          text:
                                              "${EquitiFormatter.formatTradeProfitOrLoss(value: state.tradeReceiptDetails?.profit ?? tradingItem.tradeDetail.profit, locale: Localizations.localeOf(context).toString())} ${state.accountCurrency!}",
                                          style: style.textXs,
                                          color: theme.text.textPrimary,
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Divider(color: theme.border.borderSecondary),
                            const SizedBox(height: 13),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    DuploText(
                                      text: loc.trader_closePriceTitle,
                                      style: style.textXs,
                                      color: theme.text.textTertiary,
                                    ),
                                    const SizedBox(height: 4),
                                    DuploText(
                                      text: loc.trader_takeProfitTitle,
                                      style: style.textXs,
                                      color: theme.text.textTertiary,
                                    ),
                                    const SizedBox(height: 4),
                                    DuploText(
                                      text: loc.trader_stopLossTitle,
                                      style: style.textXs,
                                      color: theme.text.textTertiary,
                                    ),
                                  ],
                                ),
                                Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    DuploText(
                                      text: EquitiFormatter.formatTradePrice(
                                        value:
                                            state
                                                .tradeReceiptDetails
                                                ?.closePrice ??
                                            0,
                                        digits:
                                            state.tradeReceiptDetails?.digits ??
                                            5,
                                        locale:
                                            Localizations.localeOf(
                                              context,
                                            ).toString(),
                                      ),
                                      style: style.textXs,
                                      color: theme.text.textPrimary,
                                    ),
                                    const SizedBox(height: 4),
                                    DuploText(
                                      text: EquitiFormatter.formatTradePrice(
                                        value:
                                            state
                                                .tradeReceiptDetails
                                                ?.takeProfit ??
                                            0,
                                        digits:
                                            state.tradeReceiptDetails?.digits ??
                                            5,
                                        locale:
                                            Localizations.localeOf(
                                              context,
                                            ).toString(),
                                      ),
                                      style: style.textXs,
                                      color: theme.text.textPrimary,
                                    ),
                                    const SizedBox(height: 4),
                                    DuploText(
                                      text: EquitiFormatter.formatTradePrice(
                                        value:
                                            state
                                                .tradeReceiptDetails
                                                ?.stopLoss ??
                                            0,
                                        digits:
                                            state.tradeReceiptDetails?.digits ??
                                            5,
                                        locale:
                                            Localizations.localeOf(
                                              context,
                                            ).toString(),
                                      ),
                                      style: style.textXs,
                                      color: theme.text.textPrimary,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 12),
                      Padding(
                        padding: const EdgeInsetsDirectional.only(
                          start: 16,
                          end: 16,
                          top: 10,
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            DuploText(
                              text: loc.trader_general,
                              style: style.textMd,
                              fontWeight: DuploFontWeight.semiBold,
                            ),
                            const SizedBox(height: 16),
                            Padding(
                              padding: const EdgeInsets.symmetric(vertical: 8),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  DuploText(
                                    text: loc.trader_tradeIDNumber,
                                    style: style.textSm,
                                    color: theme.text.textSecondary,
                                    fontWeight: DuploFontWeight.medium,
                                  ),
                                  DuploText(
                                    text:
                                        EquitiFormatter.formatAtMost6TrimmedDecimal(
                                          value:
                                              state.tradeReceiptDetails?.id ??
                                              tradingItem.id,
                                          locale:
                                              Localizations.localeOf(
                                                context,
                                              ).toString(),
                                        ),
                                    style: style.textSm,
                                    color: theme.text.textPrimary,
                                    fontWeight: DuploFontWeight.medium,
                                  ),
                                ],
                              ),
                            ),
                            Divider(color: theme.border.borderSecondary),
                            Padding(
                              padding: const EdgeInsets.symmetric(vertical: 8),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  DuploText(
                                    text: loc.trader_product,
                                    style: style.textSm,
                                    color: theme.text.textSecondary,
                                    fontWeight: DuploFontWeight.medium,
                                  ),
                                  DuploText(
                                    text:
                                        (state
                                                    .tradeReceiptDetails
                                                    ?.tickerName
                                                    ?.isNotEmpty ??
                                                false)
                                            ? state
                                                .tradeReceiptDetails
                                                ?.tickerName
                                            : tradingItem
                                                .tradeDetail
                                                .tickerName,
                                    style: style.textSm,
                                    color: theme.text.textPrimary,
                                    fontWeight: DuploFontWeight.medium,
                                  ),
                                ],
                              ),
                            ),
                            Divider(color: theme.border.borderSecondary),
                            Padding(
                              padding: const EdgeInsets.symmetric(vertical: 8),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  DuploText(
                                    text: loc.trader_baseCurrency,
                                    style: style.textSm,
                                    color: theme.text.textSecondary,
                                    fontWeight: DuploFontWeight.medium,
                                  ),
                                  DuploText(
                                    text:
                                        state
                                            .tradeReceiptDetails
                                            ?.baseCurrency ??
                                        "",
                                    style: style.textSm,
                                    color: theme.text.textPrimary,
                                    fontWeight: DuploFontWeight.medium,
                                  ),
                                ],
                              ),
                            ),
                            Divider(color: theme.border.borderSecondary),
                            Padding(
                              padding: const EdgeInsets.symmetric(vertical: 8),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  DuploText(
                                    text: loc.trader_marketClass,
                                    style: style.textSm,
                                    color: theme.text.textSecondary,
                                    fontWeight: DuploFontWeight.medium,
                                  ),
                                  DuploText(
                                    text:
                                        state
                                            .tradeReceiptDetails
                                            ?.productCategroyName ??
                                        "",
                                    style: style.textSm,
                                    color: theme.text.textPrimary,
                                    fontWeight: DuploFontWeight.medium,
                                  ),
                                ],
                              ),
                            ),
                            Divider(color: theme.border.borderSecondary),
                            Padding(
                              padding: const EdgeInsets.symmetric(vertical: 8),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  DuploText(
                                    text: loc.trader_marketType,
                                    style: style.textSm,
                                    color: theme.text.textSecondary,
                                    fontWeight: DuploFontWeight.medium,
                                  ),
                                  DuploText(
                                    text:
                                        state
                                            .tradeReceiptDetails
                                            ?.assetTypeName ??
                                        "",
                                    style: style.textSm,
                                    color: theme.text.textPrimary,
                                    fontWeight: DuploFontWeight.medium,
                                  ),
                                ],
                              ),
                            ),
                            Divider(color: theme.border.borderSecondary),
                          ],
                        ),
                      ),
                      const SizedBox(height: 33),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 24),
                        child: Column(
                          children: [
                            DuploText(
                              text: loc.trader_needHelpWithThis,
                              style: style.textSm,
                              color: theme.text.textSecondary,
                            ),
                            const SizedBox(height: 10),
                            DuploButton.secondary(
                              useFullWidth: true,
                              title: loc.trader_contactUs,
                              onTap: () {
                                try {
                                  final localeManager =
                                      diContainer<LocaleManager>();
                                  final themeManager =
                                      diContainer<ThemeManager>();
                                  diContainer<CustomerSupportChat>().openChat(
                                    locale:
                                        localeManager.getLanguageLocalCode(),
                                    isDarkMode: themeManager.isDarkMode,
                                    brokerCode: 'equitisca',
                                    brokerName: 'Equiti UAE',
                                    clientAccountExternalId: '',
                                  );
                                } on PlatformException catch (e) {
                                  diContainer<LoggerBase>().logError(
                                    e.toString(),
                                  );
                                }
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              _ => const SizedBox.shrink(),
            };
          },
        ),
      ),
    );
  }
}
