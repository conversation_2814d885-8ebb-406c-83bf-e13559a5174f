import 'package:domain/domain.dart';
import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/data/api/symbol_quote_model.dart';
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/flags/trader_flags.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/buy_sell/buy_sell_buttons.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/create_order/create_trade_bloc.dart';
import 'package:e_trader/src/presentation/create_trade/create_trade_widget.dart';
import 'package:e_trader/src/presentation/model/buy_sell_button_state.dart';
import 'package:e_trader/src/presentation/model/symbol_detail_view_model.dart';
import 'package:e_trader/src/presentation/portfolio/orders/bloc/orders_bloc.dart';
import 'package:e_trader/src/presentation/portfolio/orders/orders_tab.dart';
import 'package:e_trader/src/presentation/portfolio/positions/bloc/position_bloc.dart';
import 'package:e_trader/src/presentation/portfolio_position/widgets/trades_tab.dart';
import 'package:e_trader/src/presentation/price_alert/price_alert_widget.dart';
import 'package:e_trader/src/presentation/product_detail_overview/widget/product_detail_overview_screen.dart';
import 'package:e_trader/src/presentation/product_details/bloc/product_details_bloc.dart';
import 'package:e_trader/src/presentation/symbols/widgets/symbol_info_widget.dart';
import 'package:e_trader/src/presentation/symbols/widgets/tab_subscription_manager.dart';
import 'package:e_trader/src/presentation/trading_chart/advance_trading_view.dart';
import 'package:e_trader/src/presentation/watchlisted_symbol_indicator/watchlisted_symbol_indicator_screen.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';
import 'package:smooth_sheets/smooth_sheets.dart';
import 'package:theme_manager/theme_manager.dart';

class ProductDetailsWidget extends StatefulWidget {
  final SymbolDetailViewModel symbolDetail;
  final String accountNumber;
  final TradeType? tradeDirection;
  final bool? popIfPositionClosed;

  const ProductDetailsWidget({
    super.key,
    required this.symbolDetail,
    required this.accountNumber,
    this.tradeDirection,
    this.popIfPositionClosed,
  });

  @override
  State<ProductDetailsWidget> createState() => _ProductDetailsWidgetState();
}

class _ProductDetailsWidgetState extends State<ProductDetailsWidget>
    with SingleTickerProviderStateMixin, PerformanceObserverMixin {
  late final SheetController _sheetController;
  late final SheetOffsetDrivenAnimation _sheetAnimation;
  late final TabController _tabController;
  late final TabSubscriptionManager _tabSubscriptionManager;
  bool _isSheetExpanded = false;
  double _snapPoint = 100;

  @override
  void onRoutePopped(Route<Object?> poppedRoute) {
    super.onRoutePopped(poppedRoute);
    if (_tabController.index == 0 &&
        poppedRoute.settings.name == 'price_alert_sheet') {
      _onSheetExpansionChanged(false);
    }
  }

  @override
  void onRoutePushed(Route<Object?> pushedRoute) {
    super.onRoutePushed(pushedRoute);
    if (_tabController.index == 0 &&
        pushedRoute.settings.name == 'price_alert_sheet') {
      _onSheetExpansionChanged(true);
    }
  }

  @override
  void initState() {
    super.initState();
    _sheetController = SheetController();
    _tabSubscriptionManager = TabSubscriptionManager();
    _tabController = TabController(
      length: 6,
      vsync: this,
      animationDuration: Duration.zero,
    );
    _sheetAnimation = SheetOffsetDrivenAnimation(
      controller: _sheetController,
      initialValue: 0.0, // Start hidden when collapsed
    );

    // Listen to tab changes and delegate to subscription manager
    _tabController.addListener(_handleTabChange);

    // Set initial active tab
    _tabSubscriptionManager.setInitialActiveTab(_tabController.index);
  }

  void _handleTabChange() {
    if (!_tabController.indexIsChanging) {
      _tabSubscriptionManager.handleTabChange(_tabController.index);
    }
  }

  void _onSheetExpansionChanged(bool isExpanded) {
    if (_isSheetExpanded != isExpanded) {
      setState(() {
        _isSheetExpanded = isExpanded;
      });
    }
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _sheetController.dispose();
    _tabController.dispose();
    _tabSubscriptionManager.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = EquitiLocalization.of(context);
    final theme = context.duploTheme;
    final TraderFlags traderFlags = diContainer<TraderFlags>();

    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create:
              (_) =>
                  diContainer<ProductDetailsBloc>()..add(
                    ProductDetailsEvent.fetchSymbolInfo(
                      widget.symbolDetail.platformName,
                    ),
                  ),
        ),
        BlocProvider(
          create:
              (_) => diContainer<PositionBloc>(
                param1: widget.symbolDetail.platformName,
              ),
        ),
        BlocProvider(
          create:
              (_) => diContainer<OrdersBloc>(
                param1: widget.symbolDetail.platformName,
              ),
        ),
        BlocProvider(
          create:
              (_) => diContainer<CreateTradeBloc>(
                param1: (
                  digits: widget.symbolDetail.digit ?? 2,
                  symbolCode: widget.symbolDetail.platformName,
                  symbolImageUrl: widget.symbolDetail.imageURL ?? "",
                  assetType: widget.symbolDetail.assetType ?? "",
                  minLot: widget.symbolDetail.minLot,
                  maxLot: widget.symbolDetail.maxLot,
                  isForex: widget.symbolDetail.isForex,
                  lotsSteps: widget.symbolDetail.lotsSteps,
                  initialTradeType: widget.tradeDirection,
                ),
              )..add(
                CreateTradeEvent.subscribe(
                  orderSize: widget.symbolDetail.minLot,
                  eventType: TradingSocketEvent.marginRequirements.register,
                ),
              ),
        ),
      ],
      child: Scaffold(
        backgroundColor: theme.background.bgPrimary,
        resizeToAvoidBottomInset: false,
        appBar: DuploAppBar(
          title: '',
          duploAppBarTextAlign: DuploAppBarTextAlign.left,
          titleWidget: SymbolInfoWidget(symbol: widget.symbolDetail),
          titleSpacing: 0.0,
          actions: [
            DuploIconButton.customColorMode(
              onTap: () {
                DuploSheet.showModalSheetV2<void>(
                  context,
                  settings: RouteSettings(name: 'price_alert_sheet'),
                  appBar: DuploAppBar(
                    title: l10n.trader_priceAlert,
                    automaticallyImplyLeading: false,
                    duploAppBarTextAlign: DuploAppBarTextAlign.left,
                    actions: [
                      IconButton(
                        icon:
                            diContainer<ThemeManager>().isDarkMode
                                ? Assets.images.closeIc.svg(
                                  colorFilter: ColorFilter.mode(
                                    DuploTheme.of(
                                      context,
                                    ).foreground.fgSecondary,
                                    BlendMode.srcIn,
                                  ),
                                )
                                : Assets.images.closeIc.svg(),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                  bottomBar: Container(height: 0),
                  content: PriceAlertWidget(
                    platformName: widget.symbolDetail.platformName,
                    symbolImageUrl: widget.symbolDetail.imageURL ?? "",
                  ),
                );
              },
              colorMode: ColorMode.dynamicMode,
              icon: trader.Assets.images.priceAlertClock.keyName,
            ),
            const SizedBox(width: 8),
            WatchlistedSymbolIndicatorScreen(
              platformName: widget.symbolDetail.platformName,
              colorMode: ColorMode.dynamicMode,
            ),
            const SizedBox(width: 16),
          ],
        ),
        body: Container(
          color:
              _tabController.index == 0
                  ? diContainer<ThemeManager>().isDarkMode
                      ? Color(0xFF131722)
                      : Color(0xFFFFFFFF)
                  : theme.background.bgSecondary,
          child: SafeArea(
            child: LayoutBuilder(
              builder: (layoutBuilderContext, constraints) {
                final tabHeight = constraints.maxHeight - _snapPoint;
                return Stack(
                  children: [
                    Positioned(
                      top: 0,
                      left: 0,
                      right: 0,
                      height: tabHeight,
                      child: ColoredBox(
                        color: theme.background.bgSecondary,
                        child: DuploTabBar(
                          tabController: _tabController,
                          tabTitles: [
                            DuploTabBarTitle(
                              text: l10n.trader_chart,
                              semanticsIdentifier: 'chart_tab',
                            ),
                            DuploTabBarTitle(
                              text: l10n.trader_portfolio_trades,
                              semanticsIdentifier: 'trades_tab',
                            ),
                            DuploTabBarTitle(
                              text: l10n.trader_orders,
                              semanticsIdentifier: 'orders_tab',
                            ),
                            DuploTabBarTitle(
                              text: l10n.trader_news,
                              semanticsIdentifier: 'news_tab',
                            ),
                            DuploTabBarTitle(
                              text: l10n.trader_events,
                              semanticsIdentifier: 'events_tab',
                            ),
                            DuploTabBarTitle(
                              text: l10n.trader_details,
                              semanticsIdentifier: 'details_tab',
                            ),
                          ],
                          isScrollable: true,
                          tabViews: [
                            Column(
                              children: [
                                BlocSelector<
                                  ProductDetailsBloc,
                                  ProductDetailsState,
                                  SymbolQuoteModel?
                                >(
                                  selector: (state) {
                                    return switch (state) {
                                      ProductDetailsSuccess(
                                        infoModel: final successInfo,
                                      ) =>
                                        successInfo,
                                      _ => null,
                                    };
                                  },
                                  builder: (blocBuilderContext, state) {
                                    if (traderFlags.showClosedMarketBanner() &&
                                        !(state?.isMarketOpen ?? false))
                                      return MarketClosedBanner(
                                        onPressed:
                                            () => _tabController.animateTo(5),
                                      );
                                    else
                                      return const SizedBox();
                                  },
                                ),
                                Expanded(
                                  child: AnimatedBuilder(
                                    animation: _sheetAnimation,
                                    builder: (animationContext, child) {
                                      // Get the current sheet expansion progress (0.0 = collapsed, 0.98 = expanded)
                                      final expansionProgress =
                                          _sheetAnimation.value;

                                      // Start showing chart when sheet is 90% collapsed (10% down from full expansion)
                                      // Create opacity that goes from 0.0 at 90% expansion to 0.98 at full collapse
                                      final opacity =
                                          expansionProgress <= 0.9
                                              ? (0.98 -
                                                      (expansionProgress / 0.9))
                                                  .clamp(0.0, 0.98)
                                              : 0.0;

                                      return _ChartContainer(
                                        shouldShow: opacity > 0.0,
                                        opacity: opacity,
                                        isSheetExpanded: _isSheetExpanded,
                                        symbolDetail: widget.symbolDetail,
                                        sheetController: _sheetController,
                                        tabController: _tabController,
                                        tabSubscriptionManager:
                                            _tabSubscriptionManager,
                                        popIfPositionClosed:
                                            widget.popIfPositionClosed,
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                            TradesTab(
                              platformName: widget.symbolDetail.platformName,
                              tabController: _tabController,
                              tabIndex: 1,
                              tabSubscriptionManager: _tabSubscriptionManager,
                            ),
                            OrdersTab(
                              platformName: widget.symbolDetail.platformName,
                              tabController: _tabController,
                              tabIndex: 2,
                              tabSubscriptionManager: _tabSubscriptionManager,
                            ),
                            NewsList(
                              keepAlive: true,
                              tickerName: widget.symbolDetail.platformName,
                            ),
                            EventsList(
                              keepAlive: true,
                              tickerName: widget.symbolDetail.platformName,
                            ),
                            ProductDetailOverviewScreen(
                              platformName: widget.symbolDetail.platformName,
                              accountNumber: widget.accountNumber,
                              digit: widget.symbolDetail.digit ?? 5,
                            ),
                          ],
                          isFlex: false,
                        ),
                      ),
                    ),
                    Positioned.fill(
                      child: SheetViewport(
                        child: Sheet(
                          dragConfiguration: SheetDragConfiguration(
                            hitTestBehavior: HitTestBehavior.deferToChild,
                          ),
                          scrollConfiguration: SheetScrollConfiguration(),
                          controller: _sheetController,
                          decoration: MaterialSheetDecoration(
                            size: SheetSize.fit,
                            borderRadius: const BorderRadius.vertical(
                              top: Radius.circular(24),
                            ),
                            color: theme.background.bgPrimary,
                          ),
                          physics: BouncingSheetPhysics(
                            behavior: DirectionAwareBouncingBehavior(
                              upward: SheetOffset(0),
                            ),
                          ),
                          initialOffset: SheetOffset.absolute(_snapPoint),
                          snapGrid: SheetSnapGrid(
                            snaps: [
                              SheetOffset.absolute(_snapPoint),
                              SheetOffset(0.98), // Full height
                            ],
                          ),
                          child: Container(
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: theme.border.borderSecondary,
                              ),
                              borderRadius: const BorderRadius.vertical(
                                top: Radius.circular(24),
                              ),
                            ),
                            child: Column(
                              children: [
                                DraggableHandle(),
                                const SizedBox(height: 12),
                                Expanded(
                                  child: Padding(
                                    padding: const EdgeInsets.only(bottom: 16),
                                    child: BlocBuilder<
                                      ProductDetailsBloc,
                                      ProductDetailsState
                                    >(
                                      buildWhen:
                                          (previous, current) =>
                                              previous != current,
                                      builder: (blocBuilderContext, state) {
                                        final info = switch (state) {
                                          ProductDetailsSuccess(
                                            infoModel: final successInfo,
                                          ) =>
                                            successInfo,
                                          _ => null,
                                        };
                                        return _PlaceTradeSheetWidget(
                                          sheetController: _sheetController,
                                          symbolQuoteModel: info,
                                          symbolDetail: widget.symbolDetail,
                                          tradeDirection: widget.tradeDirection,
                                          onSheetExpansionChanged:
                                              _onSheetExpansionChanged,
                                          onBannerPressed: () {
                                            _sheetController.animateTo(
                                              SheetOffset.absolute(_snapPoint),
                                              curve: Curves.fastOutSlowIn,
                                            );
                                            _onSheetExpansionChanged(false);
                                            // Navigate to details tab
                                            _tabController.animateTo(5);
                                          },
                                        );
                                      },
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}

class _PlaceTradeSheetWidget extends StatefulWidget {
  const _PlaceTradeSheetWidget({
    required SheetController sheetController,
    required this.symbolQuoteModel,
    required this.symbolDetail,
    required this.tradeDirection,
    required this.onSheetExpansionChanged,
    this.onBannerPressed,
  }) : _sheetController = sheetController;

  final SheetController _sheetController;
  final SymbolQuoteModel? symbolQuoteModel;
  final SymbolDetailViewModel symbolDetail;
  final TradeType? tradeDirection;
  final void Function(bool isExpanded) onSheetExpansionChanged;
  final VoidCallback? onBannerPressed;

  @override
  State<_PlaceTradeSheetWidget> createState() => _PlaceTradeSheetWidgetState();
}

class _PlaceTradeSheetWidgetState extends State<_PlaceTradeSheetWidget>
    with RouteAwareAppLifecycleMixin {
  late final SheetOffsetDrivenAnimation _sheetAnimation;
  bool _isExpanded = false;
  double _previousOffset = 0.0;

  @override
  void initState() {
    super.initState();
    _sheetAnimation = SheetOffsetDrivenAnimation(
      controller: widget._sheetController,
      initialValue: 0.0, // Start hidden when collapsed
    );
    _sheetAnimation.addListener(_sheetAnimationListener);
  }

  void _sheetAnimationListener() {
    final currentOffset = widget._sheetController.metrics?.offset ?? 0.0;
    final minOffset = widget._sheetController.metrics?.minOffset ?? 0.0;
    final maxOffset = widget._sheetController.metrics?.maxOffset ?? 0.98;

    // Calculate the threshold as a percentage between min and max
    // Use 95% threshold for both expansion and collapse
    const thresholdPercentage = 0.9;
    final threshold = maxOffset - (maxOffset - minOffset) * thresholdPercentage;

    // Determine if sheet is expanding (moving up) or collapsing (moving down)
    final isExpanding = currentOffset > _previousOffset;
    final isCollapsing = currentOffset < _previousOffset;

    // Disable clicks when sheet crosses 95% threshold during expansion
    if (currentOffset >= threshold && isExpanding && !_isExpanded) {
      setState(() {
        _isExpanded = true;
      });
      // Notify parent about expansion state change
      widget.onSheetExpansionChanged(true);
    }

    // Re-enable clicks when sheet crosses 95% threshold during collapse
    if (currentOffset <= threshold && isCollapsing && _isExpanded) {
      // Always preserve user's trade selection - never reset it
      setState(() {
        _isExpanded = false;
      });
      // Notify parent about expansion state change
      widget.onSheetExpansionChanged(false);
    }

    // Update previous offset for next comparison
    _previousOffset = currentOffset;
  }

  @override
  dispose() {
    _sheetAnimation.removeListener(_sheetAnimationListener);
    super.dispose();
  }

  @override
  Widget build(BuildContext buildContext) {
    final theme = buildContext.duploTheme;
    final traderFlags = diContainer<TraderFlags>();

    return BlocSelector<ProductDetailsBloc, ProductDetailsState, bool>(
      // Use BlocSelector to get consistent market status from ProductDetailsBloc
      selector: (state) {
        final quoteModel = switch (state) {
          ProductDetailsSuccess(infoModel: final successInfo) => successInfo,
          _ => null,
        };
        return traderFlags.showClosedMarketBanner() &&
            !(quoteModel?.isMarketOpen ?? false);
      },
      builder: (context, isMarketClosed) {
        return BlocBuilder<CreateTradeBloc, CreateTradeState>(
          buildWhen: (previous, current) => previous != current,
          builder: (builderContext, createTradeState) {
            // Disable BuySellButtons only when market is closed AND on market order tab (index 0)
            final shouldDisableBuySell =
                isMarketClosed && createTradeState.currentTabIndex == 0;

            final buyState = createTradeState.buyButtonState(
              forceDisabled: shouldDisableBuySell,
            );
            final sellState = createTradeState.sellButtonState(
              forceDisabled: shouldDisableBuySell,
            );

            return Container(
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(24),
                ),
              ),
              child: SheetContentScaffold(
                backgroundColor: theme.background.bgPrimary,
                topBar: AnimatedBuilder(
                  animation: _sheetAnimation,
                  builder: (animatedBuilderContext, child) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: BuySellButtons(
                        onTap: (tradeType) {
                          // Always animate sheet up, even when disabled
                          widget._sheetController.animateTo(
                            SheetOffset.absolute(
                              widget._sheetController.metrics?.maxOffset ??
                                  0.98,
                            ),
                            curve: Curves.fastOutSlowIn,
                          );
                          // Only update trade type if not disabled
                          if (!shouldDisableBuySell &&
                              tradeType != createTradeState.tradeType) {
                            animatedBuilderContext.read<CreateTradeBloc>().add(
                              CreateTradeEvent.updateTradeType(tradeType),
                            );
                          }
                        },
                        spread: widget.symbolQuoteModel?.spread ?? 0,
                        digits: widget.symbolQuoteModel?.digits ?? 2,
                        buyButtonState:
                            buyState ??
                            BuySellButtonState.active(
                              widget.symbolQuoteModel?.ask ?? 0.0,
                            ),
                        sellButtonState:
                            sellState ??
                            BuySellButtonState.active(
                              widget.symbolQuoteModel?.bid ?? 0.0,
                            ),
                      ),
                    );
                  },
                ),
                body: AnimatedBuilder(
                  animation: _sheetAnimation,
                  child: CreateTradeWidget(
                    args: (
                      digits: widget.symbolQuoteModel?.digits ?? 2,
                      symbolCode: widget.symbolDetail.platformName,
                      symbolImageUrl: widget.symbolDetail.imageURL ?? "",
                      assetType: widget.symbolDetail.assetType ?? "",
                      minLot: widget.symbolDetail.minLot,
                      maxLot: widget.symbolDetail.maxLot,
                      isForex: widget.symbolDetail.isForex,
                      lotsSteps: widget.symbolDetail.lotsSteps,
                      initialTradeType: widget.tradeDirection,
                    ),
                    onBannerPressed: widget.onBannerPressed,
                  ),
                  builder: (animationContext, child) {
                    return FadeTransition(
                      opacity: _sheetAnimation,
                      child: child,
                    );
                  },
                ),
              ),
            );
          },
        );
      },
    );
  }
}

class _ChartContainer extends StatelessWidget {
  final bool shouldShow;
  final double opacity;
  final bool isSheetExpanded;
  final SymbolDetailViewModel symbolDetail;
  final SheetController sheetController;
  final TabController tabController;
  final TabSubscriptionManager tabSubscriptionManager;
  final bool? popIfPositionClosed;

  const _ChartContainer({
    required this.shouldShow,
    required this.opacity,
    required this.isSheetExpanded,
    required this.symbolDetail,
    required this.sheetController,
    required this.tabController,
    required this.tabSubscriptionManager,
    this.popIfPositionClosed,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Always keep chart in widget tree but position it offscreen when hidden
        PositionedDirectional(
          start: shouldShow ? 0 : -10000, // Move offscreen when hidden
          top: 0,
          end: shouldShow ? 0 : null,
          bottom: shouldShow ? 0 : null,
          width: shouldShow ? null : 1, // Minimal width when hidden
          height: shouldShow ? null : 1,
          child: AdvanceTradingView(
            platformName: symbolDetail.platformName,
            tickerName: symbolDetail.symbolName,
            digit: symbolDetail.digit ?? 5,
            sheetController: sheetController,
            interactionsEnabled: !isSheetExpanded,
            tabController: tabController,
            tabIndex: 0, // Chart tab is at index 0
            tabSubscriptionManager: tabSubscriptionManager,
            popIfPositionClosed: popIfPositionClosed,
          ),
        ),
      ],
    );
  }
}
