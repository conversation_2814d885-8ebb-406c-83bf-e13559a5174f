import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/model/close_position_type.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/navigation/equiti_trader_navigation.dart';
import 'package:e_trader/src/presentation/create_trade/widgets/input_order_size_widget.dart';
import 'package:e_trader/src/presentation/model/trade_confirmation_result.dart';
import 'package:e_trader/src/presentation/partial_close/bloc/partial_close_bloc.dart';
import 'package:e_trader/src/presentation/positions_and_trades/close_trade_tile.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

Future<void> showChartClosepositionSheet({
  required BuildContext context,
  required String positionId,
  required String platformName,
  required void Function() onTap,
  bool popIfPositionClosed = false,
}) async {
  await DuploSheet.showModalSheetV2<void>(
    context,
    appBar: DuploAppBar(
      titleWidget: DuploText(
        text: EquitiLocalization.of(context).trader_closeTrade,
        style: context.duploTextStyles.textLg,
        fontWeight: DuploFontWeight.bold,
        color: context.duploTheme.text.textPrimary,
      ),
      title: EquitiLocalization.of(context).trader_closeTrade,
      automaticallyImplyLeading: false,
      duploAppBarTextAlign: DuploAppBarTextAlign.left,
    ),

    content: Padding(
      padding: const EdgeInsets.fromLTRB(16.0, 0.0, 16.0, 32),
      child: BlocProvider(
        create:
            (_) =>
                diContainer<PartialCloseBloc>()..add(
                  PartialCloseEvent.onLoadPosition(
                    positionId.toInt(),
                    platformName,
                  ),
                ),
        child: BlocBuilder<PartialCloseBloc, PartialCloseState>(
          buildWhen: (previous, current) => previous != current,
          builder: (ctx, state) {
            final theme = DuploTheme.of(context);

            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CloseTradeTile(
                  lots: state.position?.lotSize ?? 0,
                  tradeType: state.position?.positionType ?? TradeType.buy,
                  tradeId: state.position?.positionId ?? '',
                  productName: state.position?.tickerName ?? '',
                  productIcon: state.position?.productLogoUrl ?? '',
                ),
                SizedBox(height: 12),
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          DuploText(
                            text:
                                EquitiLocalization.of(context).trader_openPrice,
                            style: context.duploTextStyles.textSm,
                            color: context.duploTheme.text.textSecondary,
                          ),
                          DuploText(
                            text: EquitiFormatter.formatTradePrice(
                              value: state.position?.openPrice ?? 0,
                              digits: state.position?.digits ?? 0,
                              locale:
                                  Localizations.localeOf(context).toString(),
                            ),
                            style: context.duploTextStyles.textMd,
                            fontWeight: DuploFontWeight.bold,
                            color: context.duploTheme.text.textPrimary,
                          ),
                        ],
                      ),
                      Assets.images
                          .chevronRightDirectional(context)
                          .svg(
                            height: 24,
                            width: 24,
                            colorFilter: ColorFilter.mode(
                              theme.border.borderPrimary,
                              BlendMode.srcIn,
                            ),
                          ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          DuploText(
                            text:
                                EquitiLocalization.of(
                                  context,
                                ).trader_currentPrice,
                            style: context.duploTextStyles.textSm,
                            color: context.duploTheme.text.textSecondary,
                          ),
                          DuploText(
                            text: EquitiFormatter.formatTradePrice(
                              value: state.position?.currentPrice ?? 0,
                              digits: state.position?.digits ?? 0,
                              locale:
                                  Localizations.localeOf(context).toString(),
                            ),
                            style: context.duploTextStyles.textMd,
                            fontWeight: DuploFontWeight.bold,
                            color: context.duploTheme.text.textPrimary,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  width: double.infinity,
                  child: DuploHorizontalTabs<ClosePositionType>.buttonBorder(
                    options: [
                      ClosePositionType.fullyClose,
                      ClosePositionType.partialClose,
                    ],
                    selectedValue: state.closePositionType,
                    onChanged: (value) {
                      ctx.read<PartialCloseBloc>().add(
                        PartialCloseEvent.closePositionTypeChanged(value),
                      );
                    },
                    semanticsIdentifierBuilder: (option) {
                      return option == ClosePositionType.fullyClose
                          ? 'close_position_fully_closed'
                          : 'close_position_partial_close';
                    },
                    textBuilder: (option) {
                      return option == ClosePositionType.fullyClose
                          ? EquitiLocalization.of(context).trader_fullyClose
                          : EquitiLocalization.of(context).trader_partialClose;
                    },
                  ),
                ),

                const SizedBox(height: 30),
                if (state.closePositionType == ClosePositionType.partialClose)
                  InputOrderSizeWidget(
                    semanticsIdentifier: 'close_position_lot_size',
                    args: (
                      minLot: state.position?.minLot ?? 0,
                      maxLot: state.position?.lotSize ?? 0,
                      initialOrderSize: state.position?.lotSize ?? 0,
                      isDisabled: false,
                      lotsSteps: state.position?.minLot ?? 0,
                    ),
                    tradeType: state.position?.positionType,
                    onOrderSizeChanged:
                        (orderSizeState) => ctx.read<PartialCloseBloc>().add(
                          PartialCloseEvent.orderSizeChanged(orderSizeState),
                        ),
                    showBorder: false,
                  ),
                if (state.closePositionType == ClosePositionType.partialClose)
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Divider(color: theme.border.borderSecondary),
                  ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    DuploText(
                      text: '${EquitiLocalization.of(context).trader_profit}: ',

                      style: context.duploTextStyles.textXs,
                      color: context.duploTheme.text.textSecondary,
                    ),
                    DuploText(
                      text:
                          (state.profit > 0 ? "+" : '') +
                          '${EquitiFormatter.formatTradeProfitOrLoss(value: state.profit, locale: Localizations.localeOf(context).toString())} ${state.currency ?? "USD"}',
                      style: context.duploTextStyles.textMd,
                      color:
                          state.profit < 0
                              ? theme.text.textErrorPrimary
                              : theme.text.textSuccessPrimary,
                      fontWeight: DuploFontWeight.bold,
                    ),
                  ],
                ),
                Divider(color: theme.border.borderSecondary),
                const SizedBox(height: 8),
                DuploButton.defaultPrimary(
                  useFullWidth: true,
                  title:
                      state.closePositionType == ClosePositionType.fullyClose
                          ? EquitiLocalization.of(context).trader_closeTrade
                          : EquitiLocalization.of(context).trader_partialClose,
                  onTap: () {
                    ctx.read<PartialCloseBloc>().add(
                      PartialCloseEvent.onCloseTrade(
                        state.position?.tickerName ?? '',
                        volume:
                            state.closePositionType ==
                                    ClosePositionType.fullyClose
                                ? state.position?.volume
                                : null,
                      ),
                    );
                    onTap();
                    Navigator.pop(context);
                    if (popIfPositionClosed) {
                      diContainer<EquitiTraderNavigation>().navigateToPortfolio(
                        result: TradeConfirmationResult.viewTrades,
                      );
                    }
                  },
                ),
                const SizedBox(height: 12),
                DuploButton.secondary(
                  useFullWidth: true,
                  title: EquitiLocalization.of(context).trader_cancel,
                  onTap: () => Navigator.pop(context),
                ),
              ],
            );
          },
        ),
      ),
    ),
    bottomBar: SizedBox(),
  );
}
