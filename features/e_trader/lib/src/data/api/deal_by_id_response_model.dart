import 'package:freezed_annotation/freezed_annotation.dart';

part 'deal_by_id_response_model.freezed.dart';
part 'deal_by_id_response_model.g.dart';

@freezed
abstract class DealByIdResponseModel with _$DealByIdResponseModel {
  const factory DealByIdResponseModel({
    int? accountNumber,
    int? positionId,
    String? symbol,
    double? openPrice,
    double? closePrice,
    double? profit,
    double? stopLoss,
    double? takeProfit,
    String? serverCode,
    double? commission,
    double? swap,
    double? rateProfit,
    double? marginRate,
    double? rawProfit,
    double? fee,
    double? volumeClosed,
    int? id,
    double? volume,
    double? tradedVolume,
    String? executionMode,
    String? marketSymbolCode,
    String? baseCurrency,
    String? baseCurrencyUsdPair,
    String? profitCurrencyUsdPair,
    String? tradeType,
    int? timeMsc,
    DateTime? date,
    String? comment,
    String? dealEntry,
    int? digits,
    String? logoUrl,
    String? friendlyName,
    String? tickerName,
    String? productName,
    String? dealReason,
    String? productCategroyName,
    String? productCategoryId,
    double? lots,
    String? assetType,
    String? assetTypeName,
  }) = _DealByIdResponseModel;

  factory DealByIdResponseModel.fromJson(Map<String, dynamic> json) =>
      _$DealByIdResponseModelFromJson(json);
}
