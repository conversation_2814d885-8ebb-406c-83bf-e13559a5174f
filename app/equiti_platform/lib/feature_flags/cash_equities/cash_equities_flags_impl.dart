import 'package:cash_equities/cash_equities.dart';
import 'package:feature_flags/feature_flags.dart';
import 'package:flutter/foundation.dart';

final class CashEquitiesFlagsImpl implements CashEquitiesFlags {
  const CashEquitiesFlagsImpl(this._flags);

  final FeatureFlagService _flags;

  @override
  bool isEnabled() => _flags.get(kCashEquitiesEnabled);

  @override
  Stream<bool> watchEnabled() => _flags.watch(kCashEquitiesEnabled);

  @override
  ValueListenable<bool> listenableEnabled() =>
      _flags.listenable(kCashEquitiesEnabled);
}
