import 'package:cash_equities/cash_equities.dart';
import 'package:e_trader/fusion.dart';
import 'package:equiti_platform/feature_flags/cash_equities/cash_equities_flags_impl.dart';
import 'package:equiti_platform/feature_flags/common/common_flags_impl.dart';
import 'package:equiti_platform/feature_flags/hub/hub_flags_impl.dart';
import 'package:equiti_platform/feature_flags/onboarding/onboarding_flags_impl.dart';
import 'package:equiti_platform/feature_flags/payment/payment_flags_impl.dart';
import 'package:equiti_platform/feature_flags/trader/trader_flags_impl.dart';
import 'package:feature_flags/feature_flags.dart';
import 'package:hub/hub.dart';
import 'package:injectable/injectable.dart';
import 'package:monitoring/monitoring.dart';
import 'package:onboarding/onboarding.dart';
import 'package:payment/payments.dart';

@module
abstract class FeatureFlagsModule {
  @lazySingleton
  FlagRegistry flagRegistry(FeatureFlagsContext context) => context.registry;

  @lazySingleton
  FeatureFlagService featureFlagService(FeatureFlagsContext context) =>
      context.service;

  @preResolve
  @lazySingleton
  Future<FeatureFlagsContext> featureFlagsContext(LoggerBase logger) {
    // Compose initial groups here; flexible to register more later via registry.registerGroup
    final initialGroups = <String, Iterable<FlagDescriptor>>{
      'hub': kHubFlagKeys,
      'common': kCommonFlagKeys,
      'payments': kPaymentFlagsKeys,
      'onboarding': kOnboardingFlagKeys,
      'e_trader': kTraderFlagsKeys,
      'cash_equities': kCashEquitiesFlagKeys,
    };
    return FeatureFlagsBootstrap.initialize(
      onError: (Object e) => logger.logError('Feature Flags error: $e'),
      initialGroups: initialGroups,
    );
  }

  @lazySingleton
  HubFlags hubFlags(FeatureFlagService service) => HubFlagsImpl(service);

  @lazySingleton
  CommonFlags commonFlags(FeatureFlagService service) =>
      CommonFlagsImpl(service);

  @lazySingleton
  PaymentFlags paymentFlags(FeatureFlagService service) =>
      PaymentFlagsImpl(service);

  @lazySingleton
  OnboardingFlags onboardingFlags(FeatureFlagService service) =>
      OnboardingFlagsImpl(service);

  @lazySingleton
  TraderFlags traderFlags(FeatureFlagService service) =>
      TraderFlagsImpl(service);

  @lazySingleton
  CashEquitiesFlags cashEquitiesFlags(FeatureFlagService service) =>
      CashEquitiesFlagsImpl(service);
}
